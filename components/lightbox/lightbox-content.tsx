import React, { FC, useEffect, useState } from 'react'
import dynamic from 'next/dynamic'
import {
  Box,
  Flex,
  Grid,
  GridItem,
  Spinner,
  Text,
  useToast,
  VStack,
  Wrap,
} from '@chakra-ui/react'
import { useSnapshot } from 'valtio'
import { PDFDownloadLink } from '@react-pdf/renderer'
import shortUuid from 'short-uuid'
import imageUrlBuilder from '@sanity/image-url'

import lightboxStore from 'data/store'
import { encodeShare } from 'lib/utility'
import Photo from '@components/photo'
import MotionBox from '@components/motion-box'
import { rawSanityClient } from '@lib/sanity'

const Pdf = dynamic(() => import('layouts/pdf'), { ssr: false })

const LightboxContent: FC<{ fetching: boolean }> = ({ fetching }) => {
  const { mediaSelected } = useSnapshot(lightboxStore)
  const [edit, setEdit] = useState(false)
  const toast = useToast()
  const [imageBuffers, setImageBuffers] = useState<string[]>([])
  const [loading, setLoading] = useState(false)

  const fetchImages = async () => {
    setLoading(true)
    const builder = imageUrlBuilder(rawSanityClient)
    const buffers: string[] = []
    mediaSelected.forEach((media) => {
      const m = builder.image(media).quality(100).url()
      buffers.push(m)
    })

    setImageBuffers(buffers)
    setLoading(false)
  }

  useEffect(() => {
    if (mediaSelected.length > 0) {
      fetchImages()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mediaSelected])

  return (
    <Box bg="brand.gray.500" minH="var(--chakra-vh)">
      <Grid
        px={8}
        pb={7}
        pt={['120px', 7]}
        height="calc(((100vh - 28px) / 12) * 3 + 58px)"
        alignContent="center"
        templateColumns="repeat(12, 1fr)"
      >
        <GridItem colStart={4} colSpan={6}>
          <VStack>
            <Text textStyle="body" color="white">
              Lightbox
            </Text>
            <Wrap flexWrap="wrap" spacingX={8} justify="center">
              <MotionBox opacity={0.5} whileHover={{ opacity: 1 }}>
                <Text
                  cursor="pointer"
                  textStyle="h2"
                  textTransform="capitalize"
                  color="white"
                  opacity={mediaSelected.length > 0 ? 1 : 0.3}
                  onClick={() => {
                    if (mediaSelected.length == 0) {
                      return
                    }

                    setEdit(!edit)
                  }}
                >
                  Edit
                </Text>
              </MotionBox>
              <MotionBox opacity={0.5} whileHover={{ opacity: 1 }}>
                {imageBuffers?.length > 0 && !loading ? (
                  <>
                    <PDFDownloadLink
                      document={
                        <Pdf
                          images={lightboxStore.mediaSelected}
                          buffers={imageBuffers}
                        />
                      }
                      fileName="OOOZE.Portfolio.pdf"
                    >
                      {({ loading }) => (
                        <Text
                          cursor="pointer"
                          textStyle="h2"
                          textTransform="capitalize"
                          color="white"
                        >
                          {loading ? 'Loading' : 'Download'}
                        </Text>
                      )}
                    </PDFDownloadLink>
                  </>
                ) : (
                  <Text
                    cursor="pointer"
                    textStyle="h2"
                    textTransform="capitalize"
                    color="white"
                    opacity={0.3}
                  >
                    Download
                  </Text>
                )}
              </MotionBox>
              <MotionBox opacity={0.5} whileHover={{ opacity: 1 }}>
                <Text
                  cursor="pointer"
                  textStyle="h2"
                  textTransform="capitalize"
                  color="white"
                  opacity={mediaSelected.length > 0 ? 1 : 0.3}
                  onClick={async () => {
                    if (mediaSelected.length == 0) {
                      return
                    }

                    const translator = shortUuid()
                    const keys = lightboxStore.mediaSelected.map((photo) => {
                      return {
                        i: photo._key,
                        p: translator.fromUUID(photo.project),
                      }
                    })

                    const bytes = await encodeShare({ s: keys })
                    const toShare = Buffer.from(bytes).toString('base64')
                    navigator.clipboard.writeText(
                      `https://${window.location.host}/lightbox?s=${toShare}`
                    )
                    // TODO: show copied
                    toast({
                      title: 'Link Copied',
                      duration: 5000,
                      status: 'success',
                      isClosable: true,
                    })
                  }}
                >
                  Share Link
                </Text>
              </MotionBox>
              <MotionBox opacity={0.5} whileHover={{ opacity: 1 }}>
                <Text
                  cursor="pointer"
                  textStyle="h2"
                  textTransform="capitalize"
                  color="white"
                  opacity={mediaSelected.length > 0 ? 1 : 0.3}
                  onClick={async () => {
                    if (mediaSelected.length == 0) {
                      return
                    }

                    const translator = shortUuid()
                    const keys = lightboxStore.mediaSelected.map((photo) => {
                      return {
                        i: photo._key,
                        p: translator.fromUUID(photo.project),
                      }
                    })

                    const bytes = await encodeShare({ s: keys })
                    const toShare = Buffer.from(bytes).toString('base64')
                    window.location.href = `mailto:?subject=Oooze Lightbox&body=https://${window.location.host}/lightbox?s=${toShare}`
                  }}
                >
                  Email
                </Text>
              </MotionBox>
              <MotionBox opacity={0.5} whileHover={{ opacity: 1 }}>
                <Text
                  cursor="pointer"
                  textStyle="h2"
                  textTransform="capitalize"
                  color="white"
                  opacity={mediaSelected.length > 0 ? 1 : 0.3}
                  onClick={() => {
                    if (mediaSelected.length == 0) {
                      return
                    }

                    lightboxStore.mediaSelected.splice(
                      0,
                      lightboxStore.mediaSelected.length
                    )
                  }}
                >
                  New Folio
                </Text>
              </MotionBox>
            </Wrap>
          </VStack>
        </GridItem>
      </Grid>
      {fetching ? (
        <Box pos="fixed" top="50%" left="50%" transform="translate(-50%, -50%)">
          <Spinner color="white" />
        </Box>
      ) : (
        <Flex
          alignContent="center"
          justifyContent="center"
          flexWrap="wrap"
          px={24}
          pb={24}
        >
          {mediaSelected.map((media, index) => {
            return (
              <Box
                pos="relative"
                key={index}
                sx={{
                  img: {
                    height: '160px',
                  },
                }}
                p={'11px'}
              >
                {media._type === 'vimeo' && media.thumbnail ? (
                  <Photo
                    photo={media.thumbnail as any}
                    useChakra
                    isThumbnail
                    withPlaceholder
                    edit={edit}
                  />
                ) : media._type === 'photo' ? (
                  <Photo
                    photo={media as any}
                    useChakra
                    isThumbnail
                    withPlaceholder
                    edit={edit}
                  />
                ) : media._type === 'video' ? (
                  <Box
                    bg="gray.200"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    h="160px"
                    color="gray.600"
                  >
                    Video
                  </Box>
                ) : null}
              </Box>
            )
          })}
        </Flex>
        // <Grid
        //   templateColumns="repeat(8, 1fr)"
        //   gap={'11px'}
        //   alignContent="center"
        //   px={24}
        //   pb={24}
        // >
        //   {mediaSelected.map((media, index) => {
        //     return (
        //       <Box
        //         pos="relative"
        //         key={index}
        //         sx={{
        //           img: {
        //             height: '160px',
        //           },
        //         }}
        //       >
        //         <Photo
        //           photo={media}
        //           useChakra
        //           isThumbnail
        //           withPlaceholder
        //           edit={edit}
        //         />
        //       </Box>
        //     )
        //   })}
        // </Grid>
      )}
    </Box>
  )
}

export default LightboxContent
